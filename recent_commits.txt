ca38d5c67|Prod alerting image updates (#3474)|<PERSON>|2025-10-07
615c1af54|Add unified-mem-oct06 to autoscaler (#3468)|<PERSON>|2025-10-07
de7fd7d39|Addressing OOM task failures for neteng (#3473)|Srinivasulu Vemana|2025-10-07
651c13e29|Topic cleanup for raw log archive writer (#3472)|<PERSON>|2025-10-06
dac5594b6|enable back autoscaler for akamai (#3471)|Srinivasulu Vemana|2025-10-06
f2113c498|Split syslog-paloalto job (#3470)|<PERSON>|2025-10-06
dc85b3770|Updating akamai task cpu to address busy node (#3469)|Srinivasulu Vemana|2025-10-06
f731ebb0c|Revert "Adjusting max parallelism for cio-int job (#3466)" (#3467)|Srinivasulu Vemana|2025-10-06
ec8a69190|Adjusting max parallelism for cio-int job (#3466)|Srinivasulu Vemana|2025-10-06
3a3e06e3d|Remove old opensearch pools from the autoscaler (#3463)|Kyle Cooley|2025-10-05
c7ce461ca|Increase cpu request omnibus-syslog (#3465)|Matthew Breeds|2025-10-04
625d0c6dc|Adjust autoscaler omnibus-syslog (#3464)|Matthew Breeds|2025-10-04
505eed015|OpenSearch worker pool replacement (#3451)|Kyle Cooley|2025-10-04
77a167d79|Increase cpu request omnibus-akamai (#3462)|Matthew Breeds|2025-10-03
c1852d77b|cleanup old sep02 unified worker pool (#3460)|Srinivasulu Vemana|2025-10-03
a770013a3|Fix uptycs cnapps config (#3461)|Kyle Cooley|2025-10-03
128cea08d|Migrate uptycs collectors (#3458)|Kyle Cooley|2025-10-03
0db801dbf|Tuning omnibus-umbrella (#3459)|Matthew Breeds|2025-10-03
9bd281c03|new unified wp oct03 (#3454)|Srinivasulu Vemana|2025-10-03
69c466853|Flink cpu tuning (#3457)|Matthew Breeds|2025-10-03
8f0d01e3f|Split palo alto across 2 topics (#3456)|MARIAN TATARU|2025-10-03
3b4e4840e|Remove replaced topic (#3455)|Kyle Cooley|2025-10-03
704683a52|Replace topic (#3453)|Kyle Cooley|2025-10-03
dbca9a8ef|Adjust hpa for os batch writer palo alto (#3452)|Kyle Cooley|2025-10-03
ce665f437|Revert "Iceberg writer (Java) deployment take 2 (#3448)" (#3450)|Kyle Cooley|2025-10-02
a888568c0|Iceberg writer (Java) deployment take 2 (#3448)|Kyle Cooley|2025-10-02
629e96977|Fix config for xforce threat score collector (#3449)|Kyle Cooley|2025-10-02
64c22e99f|Add the remaining Sigma levels to the level enum. (#3447)|Hunter Madison|2025-10-02
da48b0f2a|Collector migration (#3403)|Kyle Cooley|2025-10-02
056307326|Autoscaler adjustment paloalto (#3438)|Matthew Breeds|2025-10-02
06243b0b8|Split work to parse data across threads in Iceberg writer (Java) (#3446)|Kyle Cooley|2025-10-02
2057ed4fd|Revert "Iceberg writer deployment (java) (#3436)" (#3445)|Kyle Cooley|2025-10-01
9182fbf10|Iceberg writer deployment (java) (#3436)|Kyle Cooley|2025-10-01
4c42084d8|Cleanup old kafka-aug22 nodepool (#3444)|Srinivasulu Vemana|2025-10-01
18be79a1c|Fix npe (#3441)|Kyle Cooley|2025-10-01
a61234c0f|Fix missing catalog implementation and other misc issues (#3430)|Kyle Cooley|2025-10-01
736b31bf6|Reduce scale down interval syslog paloalto and cisco (#3437)|Matthew Breeds|2025-10-01
ce74461db|Reduce the number of tasks created when evaluating Sigma rules. (#3433)|Hunter Madison|2025-10-01
f8594eeb1|Prod data processing image updates (#3432)|Kyle Cooley|2025-10-01
82b4b9975|Jp/prom server oom fix (#3431)|James Parker|2025-10-01
250b41f79|migrate kafka to new node pool sep30 (#3426)|Srinivasulu Vemana|2025-10-01
7053b3395|Reduce number of rules (#3429)|Kyle Cooley|2025-09-30
456f32eac|Increment version (#3428)|Kyle Cooley|2025-09-30
f01cd2516|Cleanup (#3427)|Kyle Cooley|2025-09-30
e06a699af|Vector cortex writer (#3425)|MARIAN TATARU|2025-09-30
b763f2817|Allow for customers to see unknown dataset errors. (#3414)|Hunter Madison|2025-09-30
31f63be10|Scale down cortex writer (#3423)|Matthew Breeds|2025-09-30
8be4284d6|Remove topics from cortex-writer (#3422)|Matthew Breeds|2025-09-30
dcf75627d|Increase memory cortex-writer (#3421)|Matthew Breeds|2025-09-30
e8404fc73|Increase memory cortex-writer (#3420)|Matthew Breeds|2025-09-30
5d3b009f6|Increase heap cortex-writer (#3419)|Matthew Breeds|2025-09-30
e014c1ad9|Scale cortex writer (#3418)|Matthew Breeds|2025-09-30
7861ec615|Temporarily scale cortex-writer (#3417)|Matthew Breeds|2025-09-30
65dd88cac|Increase memory cortex writer (#3416)|Matthew Breeds|2025-09-30
b41326b98|Increase TM heap cortex-writer (#3415)|Matthew Breeds|2025-09-30
828fb2bef|Fix config parsing (#3413)|Kyle Cooley|2025-09-30
dfbc78406|Flip cortex-writer to OCSF (#2526)|MARIAN TATARU|2025-09-30
e526ea37c|Cleanup old alerts nodepool (#3412)|Srinivasulu Vemana|2025-09-29
7a38bb8a8|rules process job label update (#3411)|Srinivasulu Vemana|2025-09-29
49a4c0121|obs unstable job label update (#3410)|Srinivasulu Vemana|2025-09-29
7d21abd9a|obs main jon label update (#3409)|Srinivasulu Vemana|2025-09-29
99e8b7ee3|Stress test the rules engine. (#3381)|Hunter Madison|2025-09-29
922de1fcd|obs paloalto-1 node label update (#3408)|Srinivasulu Vemana|2025-09-29
337fadd8c|Adjust os keepalive settings to match pod defaults (#3393)|Kyle Cooley|2025-09-29
479ab0009|obs job label update for alert workload (#3406)|Srinivasulu Vemana|2025-09-29
4634c5b33|Enable autoscaling syslog cisco and paloalto (#3407)|Matthew Breeds|2025-09-29
e54bfb548|Prod data processing image updates (#3405)|Kyle Cooley|2025-09-29
6aebc14e7|Adjust tolerance for nodes that have left (#3396)|Kyle Cooley|2025-09-29
48ea4503e|observation-processor-wafx-1 job node label update (#3404)|Srinivasulu Vemana|2025-09-29
1a21eac0c|Prod data collection image updates (#3401)|Kyle Cooley|2025-09-29
911f44b33|Disable autoscaler syslog paloalto and cisco (#3402)|Matthew Breeds|2025-09-29
a1ff06d0f|Migrating workload wafx (#3388)|Matthew Breeds|2025-09-29
d11e39bf7|Add alerts-workload-sep26 to autoscaler config (#3386)|Matthew Breeds|2025-09-29
7c88b57c2|Iceberg writer (java) (#3394)|Kyle Cooley|2025-09-29
58f66225a|Handle edge case in opensearch batch writer (#3397)|Kyle Cooley|2025-09-29
f5a6f1d04|Increase timeout between envoy and opensearch (#3392)|Kyle Cooley|2025-09-29
c2484e470|Handle Bloom Filters in Rules (#3366)|Hunter Madison|2025-09-29
0f2b75800|Autoscaler tuning syslog paloalto and cisco (#3395)|Matthew Breeds|2025-09-29
fca14b317|Adjust max memory maps and workaround potential issue with arenas (#3385)|Kyle Cooley|2025-09-27
ffd66a207|Disable the opensearch dev console (#3391)|Kyle Cooley|2025-09-27
fbb5806a6|Revert "Temporarily disable snapshot management (#3387)" (#3390)|Kyle Cooley|2025-09-26
598d8d8cf|More cores for os masters (#3389)|Kyle Cooley|2025-09-26
9c8249a0f|Temporarily disable snapshot management (#3387)|Kyle Cooley|2025-09-26
54ab35116|Deploy sos s3 flow to production (#3384)|Hunter Madison|2025-09-26
48098f0ae|Add os snapshot nodes (#3383)|Kyle Cooley|2025-09-26
3f7b84831|Adjust opensearch batch writer requests (#3382)|Kyle Cooley|2025-09-26
40a58b3a0|Revert "Force merge to 1 segment (#3365)" (#3380)|Kyle Cooley|2025-09-26
bbb9542f7|Revert "Increasing max replicas for shared to 20 (#3378)" (#3379)|Srinivasulu Vemana|2025-09-26
549b18d3d|Increasing max replicas for shared to 20 (#3378)|Srinivasulu Vemana|2025-09-26
a644c5f7d|Flink "Legacy" Code Clean-up (#3292)|Hunter Madison|2025-09-25
4735bbbc9|Increment version (#3374)|Kyle Cooley|2025-09-25
1cd798db6|Tune autoscaler syslog-paloalto (#3373)|Matthew Breeds|2025-09-25
3d395ae04|CIO INT Job tunning (#3372)|Srinivasulu Vemana|2025-09-25
460484628|Revert "Disable autoscaler omnibus-syslog (#3367)" (#3370)|Matthew Breeds|2025-09-25
65471cbfb|Update opensearch dashboard with more disk metrics (#3368)|Kyle Cooley|2025-09-25
2df6f1462|Lower maxUtilization omnibus (#3369)|Matthew Breeds|2025-09-25
6f7951738|Disable autoscaler omnibus-syslog (#3367)|Matthew Breeds|2025-09-25
0ba376c81|Cortex Writer remove filter (#3363)|MARIAN TATARU|2025-09-25
1e6772a3c|Force merge to 1 segment (#3365)|Kyle Cooley|2025-09-25
9c955e1ac|Try disabling splits in omnibus (#3361)|Kyle Cooley|2025-09-25
3566876d5|Remove old flink detections processor job (#3364)|Kyle Cooley|2025-09-25
87b9010e3|Update data processing prod images (#3362)|Matthew Breeds|2025-09-24
67f3cc9bb|Make split configurable (#3358)|Kyle Cooley|2025-09-24
747beb455|Increase fetchParallelism syslog-paloalto (#3357)|Matthew Breeds|2025-09-24
cd79fa669|Tenant related enhancements (#3356)|Hunter Madison|2025-09-24
52d6da365|Cleanup trino old node pool (#3355)|Srinivasulu Vemana|2025-09-23
b6994c782|trini-sep23 new worker pool (#3354)|Srinivasulu Vemana|2025-09-23
37f80971f|Disable Kafka output for jobs that don't use it (#3353)|Kyle Cooley|2025-09-23
63211c8e8|Add kafka config (#3351)|Hunter Madison|2025-09-23
e24ca1cf6|omnibus syslog sos max parallelism increase (#3352)|Srinivasulu Vemana|2025-09-23
133375a75|Pull data in from onboarding form onto tenants. (#3346)|Hunter Madison|2025-09-22
af870f965|increase cpu to 4.5 for fdr job (#3350)|Srinivasulu Vemana|2025-09-22
13a165254|Prod data processing image updates (#3347)|Kyle Cooley|2025-09-22
052b503d4|Increase cpu for fdr job tasks (#3348)|Srinivasulu Vemana|2025-09-22
f97cedb40|Increase maxReplicas batch-writer-paloalto (#3345)|Matthew Breeds|2025-09-21
93013f9cc|Scale down opensearch warm pools (#3344)|Kyle Cooley|2025-09-21
f3054bc85|Increase snapshot capacity (#3343)|Kyle Cooley|2025-09-21
53a136bc9|Workaround ISM bug (#3342)|Kyle Cooley|2025-09-21
22c61b17e|Rework ISM policies (#3341)|Kyle Cooley|2025-09-21
16546c809|Adjust snapshot strategy (#3340)|Kyle Cooley|2025-09-20
da3656713|Fix log (#3339)|Kyle Cooley|2025-09-20
d7af3ac89|Fix remote index allocation issue (#3338)|Kyle Cooley|2025-09-20
d3a1b0a42|Fix snapshots process (#3337)|Kyle Cooley|2025-09-19
fa46ca853|Resume creation of snapshots and adjust cold ism policy (#3336)|Kyle Cooley|2025-09-19
9c2bfcae9|Set autoscaler to AVG utilization syslog-paloalto (#3335)|Matthew Breeds|2025-09-19
0356e9286|Increase snapshot upload rate (#3334)|Kyle Cooley|2025-09-19
095b11275|Enable autoscaler syslog-paloalto (#3333)|Matthew Breeds|2025-09-19
5b7db13e4|Temporarily disable creation of snapshots (#3332)|Kyle Cooley|2025-09-19
33a5d383b|Update data collection prod images (#3318)|MARIAN TATARU|2025-09-19
b1bad0043|Adjust and disable autoscaler sys-paloalto (#3331)|Matthew Breeds|2025-09-19
da16450dd|OCSF schema add more http_methods (#3307)|MARIAN TATARU|2025-09-19
550e45164|Merge pull request #3314 from CISO-Magna/hm/rules-engine-response-streams|Hunter Madison|2025-09-19
889d6b1f8|Update detect secrets|Hunter Madison|2025-09-19
7b7534515|Fix failing test|Hunter Madison|2025-09-19
6a3c3e51b|Setup searchable snapshots (#3322)|Kyle Cooley|2025-09-19
cc5589f9a|Increase parallelism iceberg sink (#3328)|Matthew Breeds|2025-09-18
ea18c8c02|Enable autoscaler syslog-paloalto (#3327)|Matthew Breeds|2025-09-18
04f420afa|Disable autoscaler syslog-paloalto (#3326)|Matthew Breeds|2025-09-18
de6e85078|Adjusting cpu req and parallelism syslog-paloalto (#3325)|Matthew Breeds|2025-09-18
b5614f8e1|Increase cpu request for syslog-linux and syslog-paloalto (#3324)|Matthew Breeds|2025-09-18
f906e2466|Remove superset (#3321)|Kyle Cooley|2025-09-18
4a626f902|Scale down iceberg-sink (#3323)|Matthew Breeds|2025-09-18
e0eb092a7|Vector allowed parsers and new omnibus datasets (#3274)|MARIAN TATARU|2025-09-18
c81472472|Bot/prod data processing image updates (#3316)|MARIAN TATARU|2025-09-18
ddfeb5bce|Update detect secrets|Hunter Madison|2025-09-18
bb7f5ac0b|Log patch application errors.|Hunter Madison|2025-09-18
179b77121|Fix condition related test failures and typos|Hunter Madison|2025-09-18
798762756|Tune omnibus-azure-eventhub (#3319)|Matthew Breeds|2025-09-18
1eddcc843|Reduce parallelism rules-processor (#3320)|Matthew Breeds|2025-09-18
827e01cc2|SOS S3 flow (#3286)|MARIAN TATARU|2025-09-18
0df0175c3|Tune rules-processor job (#3317)|Matthew Breeds|2025-09-18
75bee7a76|blue-fringe-sep15 (#3276)|Srinivasulu Vemana|2025-09-18
e30400cbd|Scale iw-aggregator (#3315)|Matthew Breeds|2025-09-18
961870260|Increase CPU request paloalto (#3313)|Matthew Breeds|2025-09-18
54275a65a|Handle moving average based detections around values.|Hunter Madison|2025-09-18
052a079a4|Increase iw-aggregator replicas (#3312)|Matthew Breeds|2025-09-18
b090bd4c1|Disable replicas, increase shards for paloalto index (#3311)|Kyle Cooley|2025-09-18
361272c23|Increase parallelism iceberg-sink (#3310)|Matthew Breeds|2025-09-17
894dc5a82|Add shard for palo alto data (#3309)|Kyle Cooley|2025-09-18
362e15767|Increase parallelism iceberg-sink (#3308)|Matthew Breeds|2025-09-17
79edfc15a|Tenants analyst access (#3300)|Hunter Madison|2025-09-17
1610ad001|Enable autoscaler omnibus-syslog (#3306)|Matthew Breeds|2025-09-17
45619b4dd|Disable autso-scaler omnibus-syslog (#3305)|Matthew Breeds|2025-09-17
90b8a2510|Increase resources to help alivate RocksDB bottlenecks (#3299)|Hunter Madison|2025-09-17
ddad9bf56|Setup opensearch snapshot repository (#3303)|Kyle Cooley|2025-09-17
8cc6fb536|Adjust dev patch for rules processor (#3304)|Matthew Breeds|2025-09-17
c8b75c3b7|Increase CPU request omnibus-syslog (#3302)|Matthew Breeds|2025-09-17
c293d9fe5|Increase cpu request paloalto (#3301)|Matthew Breeds|2025-09-17
c5a6d0fb8|Handle moving average based alerts|Hunter Madison|2025-09-17
42e42de5c|Support testing for exsistance|Hunter Madison|2025-09-17
e0ed58c47|Update collector http settings (#3298)|Kyle Cooley|2025-09-16
e92a0d2df|Prod data collection image updates (#3297)|Kyle Cooley|2025-09-16
14aa11c9f|cleanup cass-sep09 nodepool (#3296)|Srinivasulu Vemana|2025-09-16
e63ea2ae9|Ensure that restored operators re-count added finding ranges.|Hunter Madison|2025-09-16
8232a6477|Reduce whitepsace in jte template and allow for output tags.|Hunter Madison|2025-09-16
a6d82f943|Better commit messages (#3293)|Kyle Cooley|2025-09-16
1a023081b|cass-sep16 nodepool (#3294)|Srinivasulu Vemana|2025-09-16
4cb10b086|Remove unnecessary syslog parsing in vector (#3295)|MARIAN TATARU|2025-09-16
a9d826370|Use unaligned checkpoints with a minimum pause interval. (#3291)|Hunter Madison|2025-09-16
3b53093e1|Update prod images (#3287)|MARIAN TATARU|2025-09-16
f3d23e0dd|Msde/MGxO365 mailbox access from administrator detection (#3257)|Malvika Sharma|2025-09-16
dd9b4bddc|Msde/MGxO365 suspicious mail rule created detection (#3281)|Malvika Sharma|2025-09-16
0d35235ac|Handle illegal encoded urls without crashing (#3288)|MARIAN TATARU|2025-09-15
cc9628e27|Remove unified-workload-mem-aug14 from autoscaler (#3285)|Matthew Breeds|2025-09-15
d4435a018|Keep the state in RocksDB across restarts. (#3284)|Hunter Madison|2025-09-15
343930acf|Update baseline (#3282)|Kyle Cooley|2025-09-15
7bd739ade|Use newer implementation of crowdstrike hosts collector (#3275)|Kyle Cooley|2025-09-15
afb628b10|Update prod images (#3279)|Kyle Cooley|2025-09-15
760953356|Add new unified-workload-mem to autoscaler (#3280)|Matthew Breeds|2025-09-15
529d07ca7|General image updates (#3278)|Kyle Cooley|2025-09-15
dc3a3a6f2|Alerting image updates (#3277)|Kyle Cooley|2025-09-15
1b82b3f03|Pre-req for remove snapshots (#3273)|Kyle Cooley|2025-09-15
200ee14ce|Undo temporary decrease for replicas on hot nodes (#3272)|Kyle Cooley|2025-09-15
511573413|Handle Flink Kryo via manual process untill Flink 2 (#3271)|Hunter Madison|2025-09-15
fa752b005|More batch writer tuning (#3270)|Kyle Cooley|2025-09-14
3a1b1de56|More tuning for batch writer (#3269)|Kyle Cooley|2025-09-14
7bb1f42e5|Tune opensearch batch writer (#3268)|Kyle Cooley|2025-09-14
a5d0dbd2b|Periodically retry failed ism actions and shard allocations (#3267)|Kyle Cooley|2025-09-14
8947f4b00|Default to DOCUMENT replication and adjust force merge (#3265)|Kyle Cooley|2025-09-14
afb9e5eb0|Fix issue with opensearch jvm not detection available cpu cores (#3264)|Kyle Cooley|2025-09-13
11ce6fb31|Remove extra newlines (#3263)|Kyle Cooley|2025-09-12
2b3c640d6|Enable processing of forward.networks data (#3262)|Kyle Cooley|2025-09-12
8fefed6e4|Update prod images (#3261)|MARIAN TATARU|2025-09-12
3f6a5ad10|Remove extra newline causing trouble (#3260)|Kyle Cooley|2025-09-12
2e6577abc|Write forward networks data to appropriate topic (#3259)|Kyle Cooley|2025-09-12
1b8771fd8|disabling Autoscaler for iceberg sink job (#3258)|Srinivasulu Vemana|2025-09-12
c1d4927af|More opensearch keep alive settings (#3240)|Kyle Cooley|2025-09-12
74054af9a|Deploy CNAPP EC2 (#3252)|MARIAN TATARU|2025-09-12
fa45c3e44|Vector sos tests and refactor (#3071)|MARIAN TATARU|2025-09-12
1f25fd49c|Update prod images (#3254)|MARIAN TATARU|2025-09-12
f41b54f2e|OpenSearch lucene tweak (#3256)|Kyle Cooley|2025-09-12
f2261112a|DE job removing bad rules (#3253)|Srinivasulu Vemana|2025-09-11
84b69f1bf|Migrate to new code42 collector (#3231)|Kyle Cooley|2025-09-11
0c67cc50b|New CNAPP collectors (#3235)|MARIAN TATARU|2025-09-11
26ed64f12|Update prod images (#3237)|Kyle Cooley|2025-09-11
af083a0a9|Fix dns issue for new ssca collector (#3251)|Kyle Cooley|2025-09-11
3b0c179fe|Revert "Disable autoscaling for fdr job (#3242)" (#3250)|Srinivasulu Vemana|2025-09-11
3d38488cd|0 replicas on os hot nodes for now (#3249)|Kyle Cooley|2025-09-11
ec766951d|Opensearch ISM force_merge settings (#3247)|MARIAN TATARU|2025-09-11
e3b0bf54a|Add missing automation to kustomization.yaml (#3239)|Kyle Cooley|2025-09-11
2c08ffffa|Revert "Increasing iceberg writer coordinator mem (#3220)" (#3248)|Srinivasulu Vemana|2025-09-11
051095929|Deploy SSCA collector. (#3227)|Hunter Madison|2025-09-11
d911bf952|Try fix (#3244)|Kyle Cooley|2025-09-11
cbfefe947|Try setting s3a threads/connections lower (#3243)|Kyle Cooley|2025-09-11
c1d3b3ab4|Disable autoscaling for fdr job (#3242)|Kyle Cooley|2025-09-11
71b4866b2|Remove redundant setting and reduce replicas for hot indices (#3241)|Kyle Cooley|2025-09-10
0116f2f20|Prevent trino from getting stuck during updates (#3234)|Kyle Cooley|2025-09-10
7bb46d5bb|Collector for ISV W3id Prod (#3014)|MARIAN TATARU|2025-09-10
d216b5134|Activating new rules (#3225)|Malvika Sharma|2025-09-10
4876045c0|Bot/prod alerting image updates (#3233)|Srinivasulu Vemana|2025-09-10
0e2673fc2|Use preprod images for rules processor for now (#3232)|Kyle Cooley|2025-09-10
397463771|Data collection image updates (#3229)|Kyle Cooley|2025-09-10
5b746cadc|Temporarily disable updates for iceberg writer image (#3228)|Kyle Cooley|2025-09-10
b2a8e85ed|Correction to obs unstable rule name (#3226)|Srinivasulu Vemana|2025-09-10
0c78fc304|Try to explicitly load the Kryo pipeline.serialization-config for rules. (#3222)|Hunter Madison|2025-09-10
6125bed9d|New rule O365xSuccessfulBruteForceDetection (#3209)|Josh Powers|2025-09-10
d1825b14f|Manually set segment replication for some indices (#3224)|Kyle Cooley|2025-09-10
83edbc0be|Migrate job to use newer implementation (#3223)|Kyle Cooley|2025-09-10
6c17daa7e|Downgrade iceberg writer image (#3221)|Kyle Cooley|2025-09-10
6c0b7ce5f|Increasing iceberg writer coordinator mem (#3220)|Srinivasulu Vemana|2025-09-09
c1df2364a|Rules Container Update / Forward / SSCA (#3207)|Hunter Madison|2025-09-09
0b9a7704e|Add rules processor image to automation (#3219)|Kyle Cooley|2025-09-09
0210dccbb|remove old nodepool cassandra aug04 from autoscaler configs (#3218)|James Parker|2025-09-09
f179187b0|Jp/update k8s cassandra np (#3213)|James Parker|2025-09-09
fbfa32fdb|Data processing image updates (#3216)|Kyle Cooley|2025-09-09
4c5de56b9|Update prod images (#3217)|James Parker|2025-09-09
cdfdc1175|Persist opensearch settings (#3212)|Kyle Cooley|2025-09-09
6851b2508|Increase magnaAppVersion to 2.0.27 (#3211)|James Parker|2025-09-09
6b174ea0f|Fix ob unstable job (#3210)|Srinivasulu Vemana|2025-09-09
623a4ab72|Adjust keep alive idle time for opensearch (#3208)|Kyle Cooley|2025-09-09
944e795f6|Temporary reduce shards for opensearch-security-auditlog (#3206)|MARIAN TATARU|2025-09-09
47f69fa4f|Jp/add iceberg sink flink patch (#3205)|James Parker|2025-09-08
2c4762d25|Handle NPE in rules engine. (#3202)|Hunter Madison|2025-09-08
e3f95e79d|Deploy rules processor via flux (#3189)|Hunter Madison|2025-09-08
2c9e40d9e|Scale down wafx (#3198)|Matthew Breeds|2025-09-05
de83cbb4a|Scale down observation-processor (#3197)|Matthew Breeds|2025-09-05
290814f07|Enable autoscaler iceberg-sink (#3196)|Matthew Breeds|2025-09-05
5937c201a|Merge pull request #3195 from CISO-Magna/hm/trino-tenants-private-endpoint|Hunter Madison|2025-09-05
7ee625458|Point to the right database|Hunter Madison|2025-09-05
cfc0a55cc|Use the private endpoint to communicate with the tenants database|Hunter Madison|2025-09-05
59b8d38b2|Revert "Merge pull request #3178 from CISO-Magna/hm/revert-tenants-in-trino"|Hunter Madison|2025-09-05
d27bc36b5|Fix flux image update getting OOMKilled (#3194)|Kyle Cooley|2025-09-04
f89221b58|Scale and tune observation-processor (#3193)|Matthew Breeds|2025-09-04
e4227b727|Bump version (#3190)|Kyle Cooley|2025-09-04
2dd35d859|Split observation-processor job (#3186)|Matthew Breeds|2025-09-04
40be19b88|Add rules processor to docker bakefile. (#3187)|Hunter Madison|2025-09-04
779feac0a|New contrib collector image (#3184)|MARIAN TATARU|2025-09-04
25886dabc|turn off autoscaler and manually set parallelism (#3188)|James Parker|2025-09-04
2cd5a4c41|Support tagging records via metadata.labels for "MSP" access. (#3185)|Hunter Madison|2025-09-04
cf388f871|Add views to handle enrichments on tenant ids. (#3180)|Hunter Madison|2025-09-04
11f6650bf|Increase parallelism syslog-cisco (#3183)|Matthew Breeds|2025-09-04
6a87b7e2b|try to trigger a scale up by increasing max parallelism (#3182)|James Parker|2025-09-04
8db3d9c0a|Refactor image automation (#3149)|Kyle Cooley|2025-09-04
ba152e8b1|Flink Sigma Rules Processor (#3167)|Hunter Madison|2025-09-04
326d37af9|More memory for beekeeper devices collector (#3177)|Kyle Cooley|2025-09-04
7461ee879|Add JsonPath support to conditions logic (#3174)|Kyle Cooley|2025-09-04
34c5fd3fd|Merge pull request #3178 from CISO-Magna/hm/revert-tenants-in-trino|Hunter Madison|2025-09-04
c16bc8dca|Remove tenants from Trino catalog.|Hunter Madison|2025-09-04
ba17c55ac|Add initial dashboard for collectors (#3176)|Kyle Cooley|2025-09-04
09fa39a17|Increase cpu request syslog-cisco (#3175)|Matthew Breeds|2025-09-04
261ea64a5|Update contrib collectors image (#3173)|Kyle Cooley|2025-09-03
1c6665798|Misc dashboard adjustments (#3172)|Kyle Cooley|2025-09-03
30dc96c23|increase nodes per zone by 1 unified mem node pool (#3170)|James Parker|2025-09-03
41ddc891f|Adjust batch writer scaling (#3171)|Kyle Cooley|2025-09-03
64c1fd548|Merge pull request #2978 from CISO-Magna/hm/tenant-db-in-trino|Hunter Madison|2025-09-03
4d7c1f855|Use the URL directly vs injecting it via an environment variable.|Hunter Madison|2025-08-14
2674ed4b5|Allow Trino to read the tenants databse.|Hunter Madison|2025-08-13
9f4a9ce47|Adjust autoscaler utilization thresholds for iceberg-sink (#3169)|Matthew Breeds|2025-09-03
625fdca95|Increase node pool size for opensearch (#3168)|Kyle Cooley|2025-09-03
a29648fd5|Move opensearch masters and dashboard pods to dedicated opensearch pool (#3166)|Kyle Cooley|2025-09-03
610a766b5|IBMidxThresholdAttackerSigSuccessfulAttemptDetection (#3161)|Joshua Gomez|2025-09-03
a6c6a786b|Split opensearch batch writer further (#3164)|Kyle Cooley|2025-09-02
5c89c1db2|increase cpu req paloalto and syslog (#3163)|Matthew Breeds|2025-09-02
ec9cf9792|remove unified aug12 from autoscaler configs (#3162)|James Parker|2025-09-02
86d068588|Temporarily scale up cold nodes to recover (#3159)|Kyle Cooley|2025-09-02
3799e59b9|Reduce shards to handle bad allocation of shards on hot nodes (#3160)|Kyle Cooley|2025-09-02
6a4ffa468|Better handle connection failures in envoy when dashboard pods restart (#3151)|Kyle Cooley|2025-09-02
7e432e4ef|Adjust iceberg-sink utilization thresholds (#3158)|Matthew Breeds|2025-09-02
748230d34|disable problematic obs-processor rules (#3157)|Matthew Breeds|2025-09-02
3772b53b7|Split up akamai siem writes to opensearch (#3155)|Kyle Cooley|2025-09-02